<template>
  <view class="all flex f-column">
    <!-- 背景装饰组件 -->
    <BackgroundDecorations />

    <div :style="{ height: `${statusBarHeight}px` }"></div>
    <wd-navbar title="概况">
      <template #left>
        <div class="f-xy-center" @click="handleOpenHomeFeedback">
          <div class="f-xy-center relative">
            <image src="/static/img/feedback.png" class="nav-icon" mode="widthFix" />
            <div class="new_message" v-if="feedbackDetail?.IsReadByCurrentUser === 0 && feedbackDetail?.newestReply"></div>
          </div>
          <div class="fon-S24 mar-L12 fon-W600">留言</div>
        </div>
      </template>
    </wd-navbar>
    <wd-notice-bar :text="noticeText" prefix="check-outline" type="info" custom-class="space" />
    <!-- 使用抽离的 DashboardHeader 组件 -->
    <DashboardHeader v-model="checked" :date-value="dateMonth" @date-change="handleDateChange" @district-change="handleDistrictChange" />
    <scroll-view class="f-1 overflow-auto" scroll-y>
      <div class="content">
        <!-- 主要统计表格 -->
        <PipelineTable :pipeline-data="Statistics.pipeline" @toggleCollapse="(i) => toggleCollapse(i, 'all')" :total-summary="pipelineTotalSummary">
          <!-- 水务所统计 -->
          <DetailTable
            :data="waterdeptData"
            :pipeline-types="pipelineTypes"
            table-type="水务所"
            title="水务所统计"
            subtitle="按水务所分类的管道统计数据"
            icon-src="/static/img/home/<USER>"
            color="#3498db"
            @toggleCollapse="(i) => toggleCollapse(i, 'waterdept')"
            :default-collapsed="true"
          />

          <!-- 网格统计 -->
          <DetailTable
            :data="gridData"
            :pipeline-types="pipelineTypes"
            table-type="网格"
            @toggleCollapse="(i) => toggleCollapse(i, 'grid')"
            title="网格统计"
            subtitle="按网格分类的管道统计数据"
            icon-src="/static/img/home/<USER>"
            color="#27ae60"
            :default-collapsed="true"
          />
        </PipelineTable>

        <!-- 设施卡片列表 -->
        <template v-for="(item, index) in Statistics.facility" :key="index">
          <FacilityCard
            :facility-data="item"
            :default-collapsed="facilityCollapsed[index] !== false"
            @main-toggle="(collapsed) => handleFacilityToggle(index, collapsed)"
            @waterdept-toggle="(collapsed) => handleWaterdeptToggle(index, collapsed)"
            @grid-toggle="(collapsed) => handleGridToggle(index, collapsed)"
            @toTrigger="handleToTrigger"
          />
        </template>

        <!-- 排水管件数据展示 -->
        <PipeFittingChart :pipe-fitting-data="Statistics.pipeFitting" :default-collapsed="false" />

        <div style="height: 20px"></div>
      </div>
    </scroll-view>
    <FeedbackPopup ref="feedbackRef" :Anchors="[0, 88]" @close="getFeedbackIssueDetai" v-if="isFeedbackHomeShow" :id="2" />
  </view>

  <wd-popup v-model="popupOpen" position="bottom" custom-class="u-popup" closable custom-style="height: 80%;">
    <DataDetailPopup v-if="isPopupShow" :popup-data="popupData" :table-title="tableTitle" />
    <FacilityDetailPopup v-else :facility-data="facilityPopupData" />
  </wd-popup>

  <wd-message-box />
  <wd-toast />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import * as SituationApi from '/services/model/general.situation.js'
import { feedbackIssueDetailApi } from '@/services/model/feedback.issue.js'

import { cache } from '@/utils/cache.js'
import FeedbackPopup from '@/components/FeedbackPopup/index.vue'
import BackgroundDecorations from '@/components/BackgroundDecorations/index.vue'
import DashboardHeader from './components/DashboardHeader.vue'
import PipelineTable from './components/PipelineTable.vue'
import DetailTable from './components/DetailTable.vue'
import FacilityCard from './components/FacilityCard.vue'
import DataDetailPopup from './components/DataDetailPopup.vue'
import FacilityDetailPopup from './components/FacilityDetailPopup.vue'
import PipeFittingChart from './components/PipeFittingChart.vue'
import { verifyToken } from '@/services/model/common.js'
import { useToast, useMessage } from 'wot-design-uni'
const isFeedbackHomeShow = ref(false)

const toast = useToast()
const message = useMessage()
const isPopupShow = ref(true)
const checked = ref(true) //是否包含区局管辖
const feedbackRef = ref(null)
const statusBarHeight = ref(0)
const dateMonth = ref(null) // 日历组件的值

onShow(async () => {
  try {
    const { data } = await verifyToken()
    if (data) {
      const { data } = await SituationApi.dateSurvey()
      dateMonth.value = data.pop().year_month
      getStatistics(dateMonth.value, checked.value)

      isFeedbackHomeShow.value = true
      getFeedbackIssueDetai()
    } else {
      message.confirm({ msg: '该页面必须内部员工可查看是否前往登录？', title: '您还未登录' }).then(() => uni.navigateTo({ url: '/pages/login/index' }))
    }
  } catch (error) {}
})

onLoad(async () => {
  statusBarHeight.value = uni.getWindowInfo().statusBarHeight
})

const Statistics = ref({ facility: [], pipeline: [] }) //facility: 设施, pipeline: 管道
const surveyData = ref({})

// 计算属性：管道总计汇总
const pipelineTotalSummary = computed(() => {
  const pipeline = Statistics.value.pipeline
  if (!pipeline || pipeline.length < 2) return { municipal: 0, plot: 0, total: 0 }

  return {
    municipal: (pipeline[0]?.municipal || 0) + (pipeline[1]?.municipal || 0),
    plot: (pipeline[0]?.plot || 0) + (pipeline[1]?.plot || 0),
    total: (pipeline[0]?.total || 0) + (pipeline[1]?.total || 0)
  }
})

// 计算属性：管道类型
const pipelineTypes = computed(() => {
  return Statistics.value.pipeline.map((item) => item.key).filter(Boolean)
})

// 计算属性：水务所数据
const waterdeptData = computed(() => {
  const pipeline = Statistics.value.pipeline
  if (!pipeline || pipeline.length < 2) return []

  const maxLength = Math.max(pipeline[0]?.waterdept?.length || 0, pipeline[1]?.waterdept?.length || 0)

  const result = []
  for (let i = 0; i < maxLength; i++) {
    const dept0 = pipeline[0]?.waterdept?.[i]
    const dept1 = pipeline[1]?.waterdept?.[i]

    if (dept0 || dept1) {
      result.push({
        key: dept0?.key || dept1?.key || '',
        types: [
          {
            name: pipeline[0]?.key || '',
            municipal: dept0?.municipal || 0,
            plot: dept0?.plot || 0,
            total: (dept0?.municipal || 0) + (dept0?.plot || 0)
          },
          {
            name: pipeline[1]?.key || '',
            municipal: dept1?.municipal || 0,
            plot: dept1?.plot || 0,
            total: (dept1?.municipal || 0) + (dept1?.plot || 0)
          }
        ]
      })
    }
  }
  return result
})

// 计算属性：网格数据
const gridData = computed(() => {
  const pipeline = Statistics.value.pipeline
  if (!pipeline || pipeline.length < 2) return []

  const maxLength = Math.max(pipeline[0]?.grid?.length || 0, pipeline[1]?.grid?.length || 0)

  const result = []
  for (let i = 0; i < maxLength; i++) {
    const grid0 = pipeline[0]?.grid?.[i]
    const grid1 = pipeline[1]?.grid?.[i]

    if (grid0 || grid1) {
      result.push({
        key: grid0?.key || grid1?.key || '',
        types: [
          {
            name: pipeline[0]?.key || '',
            municipal: grid0?.municipal || 0,
            plot: grid0?.plot || 0,
            total: (grid0?.municipal || 0) + (grid0?.plot || 0)
          },
          {
            name: pipeline[1]?.key || '',
            municipal: grid1?.municipal || 0,
            plot: grid1?.plot || 0,
            total: (grid1?.municipal || 0) + (grid1?.plot || 0)
          }
        ]
      })
    }
  }
  return result
})
async function getStatistics(date, checked) {
  toast.loading('数据加载中...')
  const { data } = await SituationApi.survey({ date, all: checked })
  if (data.length === 0) {
    const { data } = await SituationApi.dateSurvey()
    toast.warning('该月份暂无数据')
    setTimeout(() => (dateMonth.value = data.pop().year_month), 1500)
    return
  }
  surveyData.value = data
  toast.close()

  // 数量统计计数
  const facility = []
  const pipeline = []
  let pipeFitting = null
  data.forEach((item) => {
    const D = { key: null, total: 0, plot: 0, municipal: 0, dataValue: null, unit: null } //plot:小区，municipal:市政
    const waterdept = {}
    const grid = {}

    D.key = item.datasource

    D.dataValue = item.data.map((Aitem) => {
      const waterdept = {}
      const grid = {}

      for (let i = 0; i < Aitem.dataValues.length; i++) {
        const item = Aitem.dataValues[i]
        if (!waterdept[item.waterdept]) waterdept[item.waterdept] = { plot: 0, municipal: 0 }
        if (!grid[item.grid]) grid[item.grid] = { plot: 0, municipal: 0 }
        if (item.pipetype == '小区') {
          waterdept[item.waterdept].plot += item.facilitycount ?? item.pipelengthkm
          grid[item.grid].plot += item.facilitycount ?? item.pipelengthkm
        } else {
          waterdept[item.waterdept].municipal += item.facilitycount ?? item.pipelengthkm
          grid[item.grid].municipal += item.facilitycount ?? item.pipelengthkm
        }
      }
      Aitem.waterdept = Object.entries(waterdept).map(([key, value]) => ({ key, ...value }))
      Aitem.grid = Object.entries(grid).map(([key, value]) => ({ key, ...value }))

      return Aitem
    })

    for (let j = 0; j < item.data.length; j++) {
      const { dataValues } = item.data[j]
      dataValues.forEach((T) => {
        const num = T.facilitycount ?? T.pipelengthkm
        if (!D.unit) T.pipelengthkm !== null ? (D.unit = 'km') : (D.unit = '个')
        D.total += num
        if (T.pipetype == '小区') D.plot += num
        if (T.pipetype == '市政') D.municipal += num

        if (!waterdept[T.waterdept]) waterdept[T.waterdept] = { plot: 0, municipal: 0 }
        if (!grid[T.grid]) grid[T.grid] = { plot: 0, municipal: 0 }
        if (T.pipetype == '小区') {
          waterdept[T.waterdept].plot += T.facilitycount ?? T.pipelengthkm
          grid[T.grid].plot += T.facilitycount ?? T.pipelengthkm
        } else {
          waterdept[T.waterdept].municipal += T.facilitycount ?? T.pipelengthkm
          grid[T.grid].municipal += T.facilitycount ?? T.pipelengthkm
        }
      })
    }
    D.waterdept = Object.entries(waterdept).map(([key, value]) => ({ key, ...value }))
    D.grid = Object.entries(grid).map(([key, value]) => ({ key, ...value }))

    if (['排水渠', '排水管'].includes(D.key)) {
      pipeline.push(D)
    } else if (D.key == '排水管件') {
      pipeFitting = D
    } else {
      facility.push(D)
    }
  })

  // 处理排水管件数据
  pipeFitting.grid = { categories: pipeFitting.grid.map(({ key }) => key), series: [{ name: '管件', data: pipeFitting.grid.map(({ municipal }) => municipal) }] }
  pipeFitting.waterdept = { categories: pipeFitting.waterdept.map(({ key }) => key), series: [{ name: '管件', data: pipeFitting.waterdept.map(({ municipal }) => municipal) }] }
  Statistics.value = { facility, pipeline, pipeFitting }

  // 初始化折叠状态为默认折叠
  initializeCollapseState(facility)
}

// 日期变化事件处理
function handleDateChange(formattedDate) {
  dateMonth.value = formattedDate
  getStatistics(formattedDate, checked.value)
}

// 区局管辖开关变化事件处理
function handleDistrictChange({ value }) {
  uni.vibrateShort({ type: 'medium' })
  getStatistics(dateMonth.value, value)
}

// 打开首页反馈页面
async function handleOpenHomeFeedback() {
  uni.vibrateShort({ type: 'medium' })
  const { data } = await verifyToken()

  if (!data) {
    return message.confirm({
      msg: '查看留言板需要进行登录',
      title: '前往登录？',
      beforeConfirm: ({ resolve }) => {
        uni.navigateTo({ url: '/pages/login/index' })
        resolve(true)
      }
    })
  }
  // 打开首页反馈页面
  feedbackRef.value.getFeedbackDetail()

  if (feedbackDetail.value?.IsReadByCurrentUser === 0 && feedbackDetail.value?.newestReply) {
    feedbackDetail.value.IsReadByCurrentUser = 1
  }
}

const feedbackDetail = ref(null)
const noticeText = ref('您目前还未登录，请先登录')
async function getFeedbackIssueDetai() {
  try {
    const token = cache.get('token')
    if (!token) return

    const { data } = await feedbackIssueDetailApi(2)
    feedbackDetail.value = data
    if (data.newestReply) {
      const reply = JSON.parse(data.newestReply)
      noticeText.value = reply.ReplyImg.length ? '[图片] ' + reply.ReplyContent : reply.ReplyContent
    } else {
      noticeText.value = '暂无最新消息'
    }
  } catch (error) {
    toast.error('数据获取错误')
  }
}

// 详细数据弹窗打开
const popupOpen = ref(false)
const popupData = ref(null)

// 折叠状态管理 - 默认折叠
const waterdeptCollapsed = ref({})
const gridCollapsed = ref({})
const facilityCollapsed = ref({})

// 初始化默认折叠状态
const initializeCollapseState = (facilityData) => {
  facilityData.forEach((_, index) => {
    if (waterdeptCollapsed.value[index] === undefined) {
      waterdeptCollapsed.value[index] = true // 默认折叠
    }
    if (gridCollapsed.value[index] === undefined) {
      gridCollapsed.value[index] = true // 默认折叠
    }
    if (facilityCollapsed.value[index] === undefined) {
      facilityCollapsed.value[index] = true // 默认折叠
    }
  })
}

// 处理设施卡片折叠状态
const handleFacilityToggle = (index, collapsed) => {
  uni.vibrateShort({ type: 'medium' })
  facilityCollapsed.value[index] = collapsed
}

// 处理水务所折叠状态
const handleWaterdeptToggle = (index, collapsed) => {
  uni.vibrateShort({ type: 'medium' })
  waterdeptCollapsed.value[index] = collapsed
}

// 处理网格折叠状态
const handleGridToggle = (index, collapsed) => {
  uni.vibrateShort({ type: 'medium' })
  gridCollapsed.value[index] = collapsed
}

const tableTitle = ref(null)
function toggleCollapse({ key }, type) {
  uni.vibrateShort({ type: 'medium' })
  let data
  if (type == 'all') {
    data = Statistics.value.pipeline.map((item) => {
      return {
        key: item.key,
        data: item.dataValue.map(({ subtype, dataValues }) => {
          const plot = dataValues
            .filter((i) => i.pipetype == '小区')
            .map(({ pipelengthkm }) => pipelengthkm)
            .reduce((a, b) => a + b, 0) //小区
          const municipal = dataValues
            .filter((i) => i.pipetype == '市政')
            .map(({ pipelengthkm }) => pipelengthkm)
            .reduce((a, b) => a + b, 0) //市政
          {
            return { subtype, data: { plot, municipal } }
          }
        })
      }
    })
  } else {
    data = Statistics.value.pipeline.map((item) => {
      return {
        key: item.key,
        data: item.dataValue.map(({ subtype, dataValues }) => {
          const T = dataValues.filter((item) => item[type] === key)
          const plot = T.find((i) => i.pipetype == '小区')?.pipelengthkm ?? 0 //小区
          const municipal = T.find((i) => i.pipetype == '市政')?.pipelengthkm ?? 0 //市政
          {
            return { subtype, data: { plot, municipal } }
          }
        })
      }
    })
  }
  tableTitle.value = key
  popupData.value = data
  isPopupShow.value = true
  popupOpen.value = true
}

const facilityPopupData = ref(null)
function handleToTrigger(val) {
  uni.vibrateShort({ type: 'medium' })
  let data
  const { key, ukey, type } = val
  if (type == 'all') {
    const D = Statistics.value.facility.find((item) => item.key === key) //筛选对应子项
    data = D.dataValue.map((item) => {
      const plot = item.dataValues.filter((i) => i.pipetype == '小区').reduce((a, b) => a + b.facilitycount, 0) //小区
      const municipal = item.dataValues.filter((i) => i.pipetype == '市政').reduce((a, b) => a + b.facilitycount, 0) //市政
      return { subtype: item.subtype, data: { plot, municipal, total: plot + municipal } }
    })
  } else {
    const D = Statistics.value.facility.find((item) => item.key === ukey) //筛选对应子项
    data = D.dataValue.map((item) => {
      const plot = item.dataValues.filter((i) => i[type] === key && i.pipetype == '小区').reduce((a, b) => a + b.facilitycount, 0) //小区
      const municipal = item.dataValues.filter((i) => i[type] === key && i.pipetype == '市政').reduce((a, b) => a + b.facilitycount, 0) //市政
      return { subtype: item.subtype, data: { plot, municipal, total: plot + municipal } }
    })
  }
  facilityPopupData.value = { key, data }
  isPopupShow.value = false
  popupOpen.value = true // 打开弹窗
}
</script>

<style lang="less" scoped>
.all {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #a5b4fc 0%, #c4b5fd 50%, #f9a8d4 100%);
  overflow: hidden;
  animation: backgroundBreathe 20s ease-in-out infinite;
}

@keyframes backgroundBreathe {
  0%,
  100% {
    background: linear-gradient(135deg, #a5b4fc 0%, #c4b5fd 50%, #f9a8d4 100%);
  }
  50% {
    background: linear-gradient(135deg, #bfdbfe 0%, #e0e7ff 50%, #fce7f3 100%);
  }
}

// 添加背景装饰层
.all::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(165, 180, 252, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(196, 181, 253, 0.15) 0%, transparent 50%);
  z-index: 0;
}

.nav-icon {
  width: 50rpx;
  height: 50rpx;
}

.new_message {
  width: 16rpx;
  height: 16rpx;
  background-color: #ff0000;
  border-radius: 100%;
  position: absolute;
  top: -2rpx;
  right: -2rpx;
  box-shadow: 2px 2px 2px #fff;
}
.content {
  padding: 12rpx 24rpx 24rpx 24rpx;
  position: relative;
  // z-index: 10;
}

// 确保导航栏和其他UI元素在背景之上
:deep(.wd-navbar) {
  position: relative;
  z-index: 20;
}

:deep(.wd-notice-bar) {
  position: relative;
  z-index: 15;
}

// 专业级动画效果
@keyframes patternFloat {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60rpx, 60rpx);
  }
}

@keyframes statusPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes headerShine {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

<style lang="less">
.wd-calendar__value {
  color: #fff !important;
}
.u-popup {
  .wd-popup__close {
    top: 30rpx !important;
    right: 32rpx !important;
    color: #fff !important;
  }
}
</style>
