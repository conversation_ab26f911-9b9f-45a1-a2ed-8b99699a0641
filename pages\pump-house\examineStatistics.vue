<template>
  <div class="examineStatistics">核查统计表</div>
</template>

<script setup>
import { ref } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import { useToast } from 'wot-design-uni'

const toast = useToast()

onLoad(({id}) =>{

})

function getPerformance(id) {
  toast.loading('正在加载...')
  PumpHouseApi.performance(id).then((res) => {
    console.log(res)
    toast.close()
  })
}
</script>

<style lang="less" scoped>
.examineStatistics {
}
</style>
