<template>
  <div class="examine-list-container">
    <!-- 任务信息卡片 -->
    <div class="task-info-card" v-if="taskInfo">
      <div class="card-header">
        <div class="task-title-section">
          <div class="task-title">{{ taskInfo.TaskName || '核查任务' }}</div>
          <div class="task-subtitle" v-if="taskInfo.description">
            <wd-icon name="edit" size="12px" color="#666" />
            <span>{{ taskInfo.description }}</span>
          </div>
        </div>
        <div class="task-status-badge" :class="getStatusClass(taskInfo)">
          <wd-icon :name="getStatusIcon(taskInfo)" size="12px" />
          <span>{{ getStatusText(taskInfo) }}</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-section" v-if="hasProgress">
        <div class="progress-info">
          <span class="progress-label">核查进度</span>
          <div class="progress-right">
            <span class="progress-status">{{ progressStatus }}</span>
            <span class="progress-value">{{ progressPercentage }}%</span>
            <span class="progress-detail" v-if="progressDetail">{{ progressDetail }}</span>
          </div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }" :class="progressClass"></div>
        </div>
      </div>

      <!-- 任务详情 -->
      <div class="task-details">
        <div class="detail-row" v-if="taskInfo.TaskEndTime">
          <div class="detail-label">
            <wd-icon name="calendar" size="14px" color="#666" />
            <span>截止时间</span>
          </div>
          <div class="detail-value" :class="{ countdown: isOverdue }">
            {{ getCountdown(taskInfo.TaskEndTime) }}
          </div>
        </div>

        <div class="detail-row" v-if="taskInfo.TaskRemark">
          <div class="detail-label">
            <wd-icon name="edit" size="14px" color="#666" />
            <span>备注</span>
          </div>
          <div class="detail-value">{{ taskInfo.TaskRemark }}</div>
        </div>
      </div>

      <!-- 卡片底部 -->
      <div class="card-footer">
        <div class="task-meta">
          <span class="task-id" v-if="taskInfo.TaskID">ID: {{ taskInfo.TaskID }}</span>
          <span class="create-time" v-if="taskInfo.CreateTime">创建于 {{ formatTime(taskInfo.CreateTime) }}</span>
        </div>
      </div>
    </div>

    <!-- 泵房列表标题 -->
    <div class="list-header">
      <div class="header-title">
        <wd-icon name="list" size="18px" color="#4299e1" />
        <span>待核查泵房列表</span>
      </div>
      <div class="header-stats" v-if="pumpRooms.length > 0">
        <span class="total-count">共 {{ pumpRooms.length }} 个泵房</span>
      </div>
    </div>

    <!-- 泵房列表内容 -->
    <div class="content-section">
      <scroll-view class="scroll-container" scroll-y="true" v-if="pumpRooms.length > 0">
        <div class="pump-room-list">
          <template v-for="(item, index) in pumpRooms" :key="index">
            <PumpRoomCard :item="item" :index="index" @click="handlePumpRoomClick" />
          </template>
        </div>
      </scroll-view>

      <!-- 空状态 -->
      <div class="empty-state" v-else>
        <div class="empty-icon">
          <wd-icon name="list" size="48px" color="#cccccc" />
        </div>
        <h3 class="empty-text">暂无泵房数据</h3>
        <p class="empty-hint">当前任务暂无需要核查的泵房</p>
      </div>
    </div>
  </div>

  <wd-popup v-model="inspectOpen" closable custom-class="all u3-popup" @close="handleClose">
    <div class="inspect-content">弹弹弹</div>
  </wd-popup>

  <wd-toast />
</template>

<script setup>
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import { useToast } from 'wot-design-uni'
import * as CommonApi from '@/services/model/common.js'
import PumpRoomCard from './components/PumpRoomCard/index.vue'

const toast = useToast()
const TaskID = ref(null)
const taskInfo = ref(null)
const pumpRooms = ref([])
const inspectOpen = ref(false)

onLoad((options) => (TaskID.value = options.id))
onShow(() => getInspectionPoint(TaskID.value))

async function getInspectionPoint(id) {
  try {
    toast.loading('正在加载...')
    const { data } = await PumpHouseApi.inspectionPoint(id)
    console.log('API返回数据:', data)

    // 根据实际API返回结构调整数据处理
    if (data) {
      taskInfo.value = data.taskInfo || data
      pumpRooms.value = data.pumpRooms || data.list || []
    }

    toast.close()
  } catch (error) {
    console.error('获取核查点数据失败:', error)
    toast.error('数据加载失败')
    toast.close()
  }
}

// 时间处理函数
function parseTimeWithTimezone(timeString) {
  if (!timeString) return null
  let date = new Date(timeString)
  const timezoneOffset = 8 * 60 * 60 * 1000
  date = new Date(date.getTime() - timezoneOffset)
  return date
}

// 判断任务是否逾期
function isTaskOverdue(endTime) {
  const end = parseTimeWithTimezone(endTime)
  if (!end) return false
  const now = new Date()
  return now > end
}

// 获取倒计时文本
function getCountdown(endTime) {
  const end = parseTimeWithTimezone(endTime)
  if (!end) return ''

  const now = new Date()
  const diff = end - now

  if (diff <= 0) {
    return '已结束'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `剩余 ${days} 天 ${hours} 小时`
  } else if (hours > 0) {
    return `剩余 ${hours} 小时 ${minutes} 分钟`
  } else if (minutes > 0) {
    return `剩余 ${minutes} 分钟`
  } else {
    return '即将到期'
  }
}

// 格式化时间
function formatTime(time) {
  const date = parseTimeWithTimezone(time)
  if (!date) return ''

  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 计算属性
const isOverdue = computed(() => {
  return taskInfo.value?.TaskEndTime && isTaskOverdue(taskInfo.value.TaskEndTime)
})

const hasProgress = computed(() => {
  return taskInfo.value?.inspectionProgress && typeof taskInfo.value.inspectionProgress === 'object'
})

const progressPercentage = computed(() => {
  if (!hasProgress.value) return 0

  const progress = taskInfo.value.inspectionProgress

  if (progress.progressPercentage !== undefined) {
    return Math.round(progress.progressPercentage)
  }

  if (progress.totalPumpRooms && progress.completedPumpRooms !== undefined) {
    const total = parseInt(progress.totalPumpRooms) || 0
    const completed = parseInt(progress.completedPumpRooms) || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  return 0
})

const progressDetail = computed(() => {
  if (!hasProgress.value) return ''

  const progress = taskInfo.value.inspectionProgress
  if (progress.totalPumpRooms !== undefined && progress.completedPumpRooms !== undefined) {
    return `${progress.completedPumpRooms}/${progress.totalPumpRooms}`
  }

  return ''
})

const progressStatus = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 100) return '已结束'
  if (percentage >= 80) return '接近结束'
  if (percentage >= 50) return '进行中'
  if (percentage > 0) return '刚开始'
  return '未开始'
})

const progressClass = computed(() => {
  const percentage = progressPercentage.value
  if (percentage >= 100) return 'progress-complete'
  if (percentage >= 80) return 'progress-high'
  if (percentage >= 50) return 'progress-medium'
  if (percentage > 0) return 'progress-low'
  return 'progress-none'
})

// 状态相关函数
function getStatusClass(task) {
  if (!task) return 'status-pending'
  if (task.status === 'completed' || task.end === 1) return 'status-completed'
  if (task.TaskEndTime && isTaskOverdue(task.TaskEndTime)) return 'status-overdue'
  return 'status-pending'
}

function getStatusIcon(task) {
  if (!task) return 'time'
  if (task.status === 'completed' || task.end === 1) return 'check-circle'
  if (task.TaskEndTime && isTaskOverdue(task.TaskEndTime)) return 'warn'
  return 'time'
}

function getStatusText(task) {
  if (!task) return '进行中'
  if (task.status === 'completed' || task.end === 1) return '已结束'
  if (task.TaskEndTime && isTaskOverdue(task.TaskEndTime)) return '已结束'
  return '进行中'
}

// 点击泵房项
async function handlePumpRoomClick(item) {
  uni.vibrateShort({ type: 'medium' })
  console.log('点击泵房:', item)
  await getPumpHouseDetail(item.PumpRoomNumber)
  inspectOpen.value = true
  // 这里可以跳转到泵房详情页面或核查页面
  // uni.navigateTo({ url: `/pages/pump-house/detail?pumpRoomNumber=${item.PumpRoomNumber}` })
}

const pumpHouseDetail = ref(null)
async function getPumpHouseDetail(pumpRoomNumber) {
  try {
    toast.loading('正在加载...')
    const { data } = await PumpHouseApi.detail(pumpRoomNumber)
    pumpHouseDetail.value = data
    console.log(data)

    toast.close()
  } catch (error) {
    toast.close()
    toast.error('详情数据加载失败')
  }
}
</script>

<style lang="less" scoped>
.examine-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24rpx;
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 20s infinite linear;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -30%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: float 15s infinite linear reverse;
    pointer-events: none;
  }
}

.inspect-content {
  width: 100%;
  height: 100%;
  background-color: rgb(255, 255, 255);
}

@keyframes float {
  0% {
    transform: rotate(0deg) translate(-20px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translate(-20px) rotate(-360deg);
  }
}

// 任务信息卡片样式
.task-info-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 10rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  overflow: hidden;
  margin-bottom: 24rpx;
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;

  // 添加卡片光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(112, 75, 75, 0.4) 50%, transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
    pointer-events: none;
  }

  animation: slideIn 0.4s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片头部
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
}

.task-title-section {
  flex: 1;
  margin-right: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.task-subtitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.task-status-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  white-space: nowrap;

  &.status-pending {
    background: linear-gradient(135deg, #fef3cd 0%, #fde68a 100%);
    color: #d97706;
    border: 1rpx solid rgba(217, 119, 6, 0.2);
  }

  &.status-completed {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #059669;
    border: 1rpx solid rgba(5, 150, 105, 0.2);
  }

  &.status-overdue {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1rpx solid rgba(220, 38, 38, 0.2);
  }
}

// 进度条部分
.progress-section {
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.3) 50%, transparent 100%);
  }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6rpx;

  &::before {
    content: '🏠';
    font-size: 20rpx;
  }
}

.progress-status {
  font-size: 22rpx;
  color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-weight: 500;
}

.progress-value {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 700;
}

.progress-detail {
  font-size: 20rpx;
  color: #4b5563;
  background: rgba(59, 130, 246, 0.1);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  font-weight: 600;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

.progress-bar {
  height: 12rpx;
  background: rgba(203, 213, 225, 0.6);
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%, rgba(255, 255, 255, 0.3) 100%);
    animation: progress-shine 2s infinite;
  }

  &.progress-none {
    background: linear-gradient(90deg, #9ca3af 0%, #6b7280 100%);
  }

  &.progress-low {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  }

  &.progress-medium {
    background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  }

  &.progress-high {
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  }

  &.progress-complete {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  }
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 任务详情
.task-details {
  padding: 20rpx 24rpx;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 140rpx;
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  flex: 1;
  font-size: 24rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;

  &.countdown {
    color: #dc2626;
    font-weight: 600;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// 卡片底部
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.task-id {
  font-size: 20rpx;
  color: #9ca3af;
  background: rgba(156, 163, 175, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.create-time {
  font-size: 20rpx;
  color: #9ca3af;
}

// 列表头部
.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);

  span {
    background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.header-stats {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.total-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.15);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

// 内容区域样式
.content-section {
  flex: 1;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.scroll-container {
  height: 100%;
  width: 100%;
}

.pump-room-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-bottom: 20rpx;
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
  position: relative;
}

.empty-icon {
  margin-bottom: 32rpx;
  opacity: 0.7;
  animation: float-gentle 3s ease-in-out infinite;

  // 添加发光效果
  filter: drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.3));
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  margin: 0 0 16rpx 0;
  line-height: 1.3;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  animation: fade-in-up 0.6s ease-out;
}

.empty-hint {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  animation: fade-in-up 0.8s ease-out;
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .examine-list-container {
    padding: 16rpx;
  }

  .task-info-card {
    margin-bottom: 16rpx;
  }

  .card-header {
    padding: 20rpx 20rpx 12rpx;
  }

  .task-title {
    font-size: 28rpx;
  }

  .task-subtitle {
    font-size: 22rpx;
  }

  .task-status-badge {
    font-size: 20rpx;
    padding: 6rpx 12rpx;
  }

  .progress-section {
    padding: 12rpx 20rpx;
  }

  .task-details {
    padding: 16rpx 20rpx;
  }

  .detail-label {
    min-width: 120rpx;
    font-size: 22rpx;
  }

  .detail-value {
    font-size: 22rpx;
  }

  .card-footer {
    padding: 12rpx 20rpx;
  }

  .task-id,
  .create-time {
    font-size: 18rpx;
  }

  .header-title {
    font-size: 28rpx;
  }

  .total-count {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }
}
</style>
<style lang="less">
.u3-popup {
  .wd-popup__close {
    color: #444444 !important;
  }
  :deep(.wd-popup__close) {
    color: #444444 !important;
  }
}
</style>
