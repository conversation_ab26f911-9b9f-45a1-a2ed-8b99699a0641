/* 全局日历样式 - 确保在微信小程序中生效 */

/* 日历弹窗层级 */
.wd-calendar {
  z-index: 9999 !important;
}

/* 日历头部样式 */
.compact-calendar .wd-calendar__header,
.wd-calendar__header {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  padding: 0 !important;
  margin-bottom: 4rpx !important;
}

/* 日历标题样式 */
.compact-calendar .wd-calendar__title,
.wd-calendar__title {
  font-weight: 600 !important;
  font-size: 24rpx !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 日历箭头样式 */
.compact-calendar .wd-calendar__arrow,
.wd-calendar__arrow {
  color: rgba(255, 255, 255, 0.7) !important;
  width: 32rpx !important;
  height: 32rpx !important;
}

/* 日历单元格样式 */
.compact-calendar .wd-calendar__cell,
.wd-calendar__cell {
  padding: 0 !important;
  background-color: transparent !important;
}

/* 日历值样式 */

/* 日历主体样式 */
.compact-calendar .wd-calendar__body,
.wd-calendar__body {
  background: transparent !important;
  border-radius: 8rpx !important;
  overflow: hidden !important;
}

/* 日历日期样式 */
.compact-calendar .wd-calendar__day,
.wd-calendar__day {
  border-radius: 6rpx !important;
  margin: 1rpx !important;
  font-size: 24rpx !important;
}

/* 选中日期样式 */
.compact-calendar .wd-calendar__day.is-selected,
.wd-calendar__day.is-selected {
  background: #764ba2 !important;
  color: white !important;
  font-weight: 600 !important;
}

/* 今天日期样式 */
.compact-calendar .wd-calendar__day.is-today,
.wd-calendar__day.is-today {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  font-weight: 600 !important;
}

/* 日历弹窗容器 */
.wd-calendar__popup {
  z-index: 9999 !important;
  position: fixed !important;
}

/* 日历弹窗背景 */
.wd-calendar__popup-mask {
  z-index: 9998 !important;
}

/* 微信小程序特殊样式 */
.compact-calendar .wd-calendar__header {
  background: rgba(0, 0, 0, 0.1) !important;
}

.compact-calendar .wd-calendar__body {
  background: rgba(0, 0, 0, 0.05) !important;
}

/* 确保日历在紫色背景上可见 */
.compact-calendar .wd-calendar__day {
  color: rgba(255, 255, 255, 0.8) !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

.compact-calendar .wd-calendar__day:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}
.wd-popup__close {
  color: #fff !important;
}
